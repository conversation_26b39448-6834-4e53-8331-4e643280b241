import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { cap, dropdownObjectiveOptions } from '../../pages/pulse/utils/helper';

interface DropdownState {
   days: string;
   channel: string;
   objective: string;
   metric: string;
   metricsOptions: { value: string; label: string }[];
   dynamicObjectives: { value: string; label: string }[];
   adgroup_ChartType: string;
}

const BASIC_KPIS = [
   'leads',
   'ctr',
   'roas',
   'spend',
   'cpc',
   'cpp',
   'clicks',
   'purchase',
];

const objectiveToMetrics: Record<string, string[]> = {
   OUTCOME_SALES: [
      'roas',
      'ctr',
      'cpp',
      'purchase',
      'clicks',
      'cpc',
      'spend',
      'frequency',
      'purchase_rate',
   ],
   LINK_CLICKS: ['clicks', 'ctr', 'cpc', 'impressions', 'spend', 'frequency'],
   OUTCOME_AWARENESS: ['reach', 'impressions', 'ctr', 'spend', 'frequency'],
   OUTCOME_LEADS: [
      'leads',
      'clicks',
      'cpl',
      'ctr',
      // 'unique_ctr',
      'reach',
      'impressions',
      'cpc',
      'spend',
      'leads_conversion_rate',
   ],
   VIDEO_VIEWS: [
      'video_watch_100_percent',
      'video_watch_95_percent',
      'video_watch_75_percent',
      'video_watch_50_percent',
      'ctr',
      'reach',
      'cpm',
      'impressions',
      'spend',
   ],
   LEAD_GENERATION: [
      'leads',
      'clicks',
      'cpl',
      'ctr',
      // 'unique_ctr',
      'reach',
      'impressions',
      'cpc',
      'spend',
      'leads_conversion_rate',
   ],
   VIDEO: ['ctr', 'impressions', 'conversions', 'cpc'],
   SEARCH: [
      'ctr',
      'cpc',
      'conversions',
      'conversion_rate',
      'cpa',
      'impressions',
      'spend',
   ],
   DISPLAY: [
      'impressions',
      'ctr',
      'conversion_rate',
      'conversions',
      'cpa',
      'spend',
   ],
   DEMAND_GEN: [
      'ctr',
      'cpc',
      'conversions',
      'cac',
      'impressions',
      'cpm',
      'spend',
   ],
   PERFORMANCE_MAX: [
      'conversions',
      'conversion_rate',
      'cpa',
      'impressions',
      'cpc',
      'spend',
   ],
   SHOPPING: [
      'ctr',
      'cpc',
      'conversions',
      'conversion_rate',
      'cpa',
      'impressions',
      'spend',
   ],
};

const initialState: DropdownState = {
   days: '7',
   channel: 'meta_ads',
   objective: '',
   metric: '',
   adgroup_ChartType: 'hour_wise',
   // metricsOptions: initialMetrics.map((metric) => ({
   //    value: metric,
   //    label: metric.toUpperCase(),
   // })),
   metricsOptions: [],
   dynamicObjectives: [],
};

const dropdownSlice = createSlice({
   name: 'dropdown',
   initialState,
   reducers: {
      setDays: (state, action: PayloadAction<string>) => {
         state.days = action.payload;
      },
      setChannel: (state, action: PayloadAction<string>) => {
         state.channel = action.payload;
      },
      setObjective: (state, action: PayloadAction<string>) => {
         state.objective = action.payload;

         const metrics = objectiveToMetrics[action.payload] || [];
         state.metricsOptions = metrics.map((metric) => ({
            value: metric,
            label: metric.toUpperCase(),
         }));
         state.metric =
            metrics.length > 0
               ? metrics.find((x) => x === state.metric) || metrics[0]
               : '';
      },
      setMetric: (state, action: PayloadAction<string>) => {
         state.metric = action.payload;
      },
      setDynamicObjectives: (state, action: PayloadAction<string[]>) => {
         state.dynamicObjectives = dropdownObjectiveOptions.filter((option) =>
            action.payload?.includes(option.value),
         );
         if (state.dynamicObjectives.length > 0) {
            state.objective = state.dynamicObjectives[0].value;
            const metrics = objectiveToMetrics[state.objective] || [
               ...BASIC_KPIS,
            ];
            state.metricsOptions = metrics.map((metric) => ({
               value: metric,
               label: metric.toUpperCase(),
            }));
            state.metric = metrics.length > 0 ? metrics[0] : '';
         } else {
            state.objective = '';
            state.metricsOptions = [];
            state.metric = '';
         }
      },

      // ------------------------------ Google ads  ---------------------- //
      setDynamicGoogleAdsObjectives: (
         state,
         action: PayloadAction<string[]>,
      ) => {
         if (action.payload.length > 0) {
            state.dynamicObjectives = action.payload.map((item) => ({
               value: item,
               label: cap(item.toLowerCase()),
            }));
            state.objective = state.dynamicObjectives[0].value;
         }
      },
      setDynamicGoogleAdsmetrics: (state, action: PayloadAction<string[]>) => {
         if (action.payload.length > 0) {
            state.metricsOptions = action.payload.map((item) => ({
               value: item,
               label: item.replace(/_/g, ' ').toUpperCase(),
            }));
            state.metric = state.metricsOptions[0]?.value;
         }
      },
      setChartMetric: (state, action: PayloadAction<string>) => {
         state.adgroup_ChartType = action.payload;
      },
   },
});

export const {
   setDays,
   setChannel,
   setObjective,
   setMetric,
   setDynamicObjectives,
   setDynamicGoogleAdsObjectives,
   setDynamicGoogleAdsmetrics,
   setChartMetric,
} = dropdownSlice.actions;
export default dropdownSlice.reducer;
