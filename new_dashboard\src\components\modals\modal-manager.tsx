import { FC } from 'react';
import { useAppSelector } from '../../store/store';
import TestModal from './test-modal';
import KPIViewModal from './card-view-details';
import SocialMediaChannelModal from './social-media-channel-modal';
import AddKPIModal from './pin-add-kpi';
import UploadImageModal from './upload-image-modal';
import TrackedKpiViewDetailsModal from './tracked-view-details';
import LinkedinPageSelect from './linkedin-page-select';
import FivetranConnectorModal from './fivetran-connector-modal';
import ShippingProfileModal from './shipping-profile/shipping-profile';
import VariableExpenseModal from './custom-expenses/variable-expense';
import FixedExpenseModal from './custom-expenses/fixed-expense';
import DeleteRecordModal from './delete-record';
import ImportCostModal from './import-cost';
import VerifyAccountDetails from './verify-account-details';
import EditAlertModal from './agents/alerting-agent/edit-alert-modal';
import PaymentModal from './PaymentModal';
import LoaderModal from './LoaderModal';

function ModalManager() {
   const currentModal = useAppSelector((state) => state.modal);

   const modalsLookup: Record<string, FC> = {
      TestModal,
      KPIViewModal,
      SocialMediaChannelModal,
      AddKPIModal,
      UploadImageModal,
      TrackedKpiViewDetailsModal,
      LinkedinPageSelect,
      FivetranConnectorModal,
      ShippingProfileModal,
      VariableExpenseModal,
      FixedExpenseModal,
      DeleteRecordModal,
      ImportCostModal,
      VerifyAccountDetails,
      EditAlertModal,
      PaymentModal,
      LoaderModal,
   };

   let renderedModal;

   if (currentModal.show && currentModal.payload) {
      const { modalType, modalProps } = currentModal.payload;

      const ModalComponent = modalsLookup[modalType];

      if (!ModalComponent) return null;

      renderedModal = <ModalComponent {...modalProps} />;
   }
   return renderedModal;
}
export default ModalManager;
