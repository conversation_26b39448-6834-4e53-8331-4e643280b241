import { useNavigate } from 'react-router-dom';
import { AgentCardProps } from './cards/agent-card';
import AgentCard from './cards/agent-card';
import AlertingAgent from '../../../assets/image/agents/Koa - Reporting Agent.png';
import MetaAdsAgentImage from '../../../assets/image/agents/Sonny - Performance Agent.png';
import AnalyticsAgentImage from '../../../assets/image/agents/Salma - Analytics Agent.png';
import DiagnosticAgentImage from '../../../assets/image/agents/Mari - Diagnostic Agent.png';
import { useAppDispatch } from '@/store/store';
import { setCurrentAgent } from '@/store/reducer/marco-reducer';

const MarcoAgents = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();

   const AGENTS: AgentCardProps[] = [
      {
         image: AlertingAgent,
         heading: 'Alerting Agent',
         description:
            'The Alerting Agent monitors KPIs, detects anomalies, and ensures accuracy. With the Diagnostic Agent, it identifies root causes and suggests fixes.',
         primaryButtonText: 'Create Alert',
         primaryButtonAction: () => {
            dispatch(setCurrentAgent('alerting-agent'));
            navigate('/marco/alerting-agent');
         },
         secondaryButtonText: 'View Alerts',
         secondaryButtonAction: () => {
            dispatch(setCurrentAgent('alerting-agent'));
            navigate('/marco/alerting-agent/alerts');
         },
      },
      {
         image: MetaAdsAgentImage,
         heading: 'Meta Ads Manager',
         description:
            'The Meta Ads Manager automates campaign creation, audience targeting, and bid optimization to maximize ad performance. It continuously analyzes data to refine strategies and improve ROI.',
         primaryButtonText: 'Create Campaign',
         primaryButtonDisabled: false,
         primaryButtonAction: () => navigate('/marco/meta-ads-manager-agent'),
         secondaryButtonText: 'Manage Campaign',
         secondaryButtonDisabled: true,
      },
      {
         image: AnalyticsAgentImage,
         heading: 'Analytics Agent',
         description:
            'The Analytics Agent connects user queries to data insights, retrieving key ad metrics and providing real-time, actionable results.',
         primaryButtonText: 'Start Analysis',
         primaryButtonDisabled: false,
         primaryButtonAction: () => {
            dispatch(setCurrentAgent('analytics-agent'));
            navigate('/marco/analytics-agent');
         },
      },
      {
         image: DiagnosticAgentImage,
         heading: 'Diagnostic Agent',
         description:
            'The Diagnostic Agent analyzes KPIs to detect performance drops, examining campaign metrics, engagement, conversions, and ad spend efficiency.',
         primaryButtonText: 'Start Diagnosis',
         primaryButtonDisabled: true,
      },
   ];

   return (
      <div className='w-full h-full px-5 py-5'>
         <div className='w-full grid gap-[30px] grid-cols-[repeat(auto-fill,_minmax(250px,_1fr))] sm:grid-cols-[repeat(auto-fill,_minmax(525px,_1fr))]'>
            {AGENTS.map((agent) => (
               <AgentCard key={agent.heading} {...agent} />
            ))}
         </div>
      </div>
   );
};

export default MarcoAgents;
