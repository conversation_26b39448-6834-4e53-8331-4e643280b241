import React, { useState } from 'react';
import { 
   Button, 
   Select, 
   VStack, 
   Text, 
   useColorModeValue,
   Flex 
} from '@chakra-ui/react';
import ModalWrapper from './modal-wrapper';
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface AccountSelectModalProps {
   title?: string;
   options: Record<string, string>;
   placeholder?: string;
   onSelect: (selectedValue: string | null) => void;
   confirmButtonText?: string;
   showCancelButton?: boolean;
}

const AccountSelectModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);
   
   const {
      title = 'Select Account',
      options = {},
      placeholder = 'Choose an account',
      onSelect,
      confirmButtonText = 'Next',
      showCancelButton = true,
   } = (payload?.modalProps || {}) as AccountSelectModalProps;

   const [selectedValue, setSelectedValue] = useState<string>('');
   const [error, setError] = useState<string>('');

   const selectBg = useColorModeValue('white', 'gray.700');
   const selectBorder = useColorModeValue('gray.300', 'gray.600');
   const errorColor = useColorModeValue('red.500', 'red.300');

   const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
      const value = event.target.value;
      setSelectedValue(value);
      if (error) setError('');
   };

   const handleConfirm = () => {
      if (!selectedValue) {
         setError('Please select an account');
         return;
      }
      
      onSelect(selectedValue);
      dispatch(closeModal());
   };

   const handleCancel = () => {
      onSelect(null);
      dispatch(closeModal());
   };

   const footer = (
      <Flex gap={3} justifyContent="flex-end" width="100%">
         {showCancelButton && (
            <Button
               variant="outline"
               onClick={handleCancel}
               colorScheme="gray"
            >
               Cancel
            </Button>
         )}
         <Button
            colorScheme="blue"
            onClick={handleConfirm}
            isDisabled={!selectedValue}
         >
            {confirmButtonText}
         </Button>
      </Flex>
   );

   return (
      <ModalWrapper
         heading={title}
         footer={footer}
         size="xl"
         closeOnEsc={true}
         closeOnOverlayClick={false}
         closeFunction={handleCancel}
      >
         <VStack spacing={3} align="stretch">
            <Select
               placeholder={placeholder}
               value={selectedValue}
               onChange={handleSelectChange}
               bg={selectBg}
               borderColor={error ? errorColor : selectBorder}
               _hover={{
                  borderColor: error ? errorColor : 'blue.300',
               }}
               _focus={{
                  borderColor: error ? errorColor : 'blue.100',
                  boxShadow: error 
                     ? '0 0 0 1px var(--chakra-colors-red-500)'
                     : '0 0 0 1px var(--chakra-colors-blue-500)',
               }}
               size="lg"
               fontSize="md"
            >
               {Object.entries(options).map(([value, label]) => (
                  <option key={value} value={value}>
                     {label}
                  </option>
               ))}
            </Select>
            
            {error && (
               <Text color={errorColor} fontSize="sm" mt={1}>
                  {error}
               </Text>
            )}
         </VStack>
      </ModalWrapper>
   );
};

export default AccountSelectModal;
