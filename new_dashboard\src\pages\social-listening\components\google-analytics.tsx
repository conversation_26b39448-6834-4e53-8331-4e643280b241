import image from '../images/integrations/googleAnalytics.png';
import Card from './Card';
import endPoints from '../apis/agent';

import LoaderModal  from '../../../components/modals/LoaderModal'
import { useEffect, useState } from 'react';
import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
import { channelNames } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import { connectToGASentiment } from '../utils';
import { dialogMessage } from '../../../utils/strings/content-manager';
import { ApiError } from './facebook-ads-form';
import { useToast } from '@chakra-ui/react';
import { GAAccountWithProperties } from '../apis/agent';
import { showAccountSelectModal, showMultiSelectModal, showConfirmationModal } from '../utils/modal-helpers';


const GoogleAnalytics = () => {
   const [isDisconnecting, setIsDisconnecting] = useState(false);
   const toast = useToast();
   const authUser = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );

   const showToast = (
      title: string,
      description: string,
      status: 'success' | 'error',
   ) => {
      toast({ title, description, status, duration: 2000, isClosable: true });
   };
   // Fetching google Analytics connection details
   const { data, isLoading, errorMessage } = useApiQuery({
      queryKey: [`gaConnectionDetails`],
      queryFn: () =>
         endPoints.checkConnectionDetails({
            client_id: authUser?.client_id || '',
            channel_name: channelNames.GOOGLE_ANALYTICS,
         }),
   });

   const {
  mutateAsync: fetchGAAccounts,
  isPending: isFetchingGA,
  //data: gaResponse,
  //errorMessage: gaErrorMessage,
} = useApiMutation<GAAccountWithProperties[], { clientId: string }>({
  mutationFn: endPoints.fetchGAAccountsAndProperties,
  onSuccessHandler: (res) => {
    console.log('Fetched GA accounts with properties:', res);
  },
  onError: (msg) => {
    console.error('Failed to fetch GA data:', msg);
  }
});
         
   const { mutate: connectToSentiment, isPending: isConnectingToSentiment } =
      useApiMutation({
         mutationFn: connectToGASentiment,
         
  onSuccessHandler: async (_data, payload) => {
         if(!payload?.isConnect){
            setTimeout(() => {
          window.location.href = `${window.location.origin}/integrations`;
        }, 1000);
        return
      
         }
      try {
        if (!authUser?.client_id) {
          showToast('Error', 'Client ID missing', 'error');
          return;
        }
        console.log('Fetching GA accounts and properties', authUser.client_id);
        
        const gaAccounts: GAAccountWithProperties[] = await fetchGAAccounts({
  clientId: authUser.client_id,
});
console.log('gaAccounts', gaAccounts);

        if (!gaAccounts.length) {
          const confirmed = await showConfirmationModal(
            'No GA accounts found',
            'Please connect to GA from the Google Analytics website',
            {
              confirmButtonText: 'OK',
              confirmButtonColor: 'blue',
              icon: 'warning',
              showCancelButton: false,
            }
          );
          if (confirmed) {
            connectToSentiment({
              client_id: authUser?.client_id,
              isConnect: false,
            });
          }
          return;
        }
        const accountOptions: Record<string, string> = {};
        gaAccounts.forEach((account) => {
          accountOptions[account.accountId] = `${account.displayName}, ${account.accountId}`;
        });
      console.log('accountOptions',accountOptions);
        const selectedAccountId = await showAccountSelectModal(accountOptions);
        if (!selectedAccountId) return;
        const selectedAccount = gaAccounts.find(
          (acc): acc is GAAccountWithProperties => acc.accountId === selectedAccountId
        );
console.log('selectedAccount',selectedAccount);
        if (!selectedAccount) {
          showToast('Error', 'Account not found', 'error');
          return;
        }
        const propertyOptions: Record<string, string> = {};
        selectedAccount.properties.forEach((prop) => {
          if (prop.propertyId && prop.displayName) {
            propertyOptions[prop.propertyId] = `${prop.displayName} [${prop.propertyId}]`;
          }
        });
console.log('propertyOptions',propertyOptions);
        const selectedPropertyIds = await showMultiSelectModal(
          'Select GA Properties',
          propertyOptions
        );
        if (!selectedPropertyIds) return;
         const confirmed = await showConfirmationModal( dialogMessage.deleteSuccess.title,
         dialogMessage.deleteSuccess.description,
         {
           
           
            icon: 'success',
            
         });
        if (!confirmed) return;
        showToast('Success', 'Connected and GA properties fetched', 'success');
        setTimeout(() => {
          window.location.href = `${window.location.origin}/integrations`;
        }, 1000);
      } catch (error) {
        showToast(
          'Warning',
          'Connected, but failed to fetch GA properties',
          'error'
        );
        console.error('Failed to fetch GA properties:', error);
      }
    },

      });



   const { is_active = false } = data?.details || {};

   async function onConnect() {
      const {
         data: { url },
      } = await endPoints.getGaAuthUrl();
      window.location.href = url;
   }

   const onDisconnect = async () => {
      const confirmed = await showConfirmationModal(
         dialogMessage.delete.title,
         dialogMessage.delete.description,
         {
            confirmButtonText: dialogMessage.delete.buttonMessage,
            cancelButtonText: 'Cancel',
            confirmButtonColor: 'blue',
            cancelButtonColor: 'gray',
            icon: 'warning',
            showCancelButton: true,
         }
      );
      if (confirmed && authUser?.client_id) {
         try {
            setIsDisconnecting(true);
            connectToSentiment({
               client_id: authUser?.client_id,
               isConnect: false,
            });
         } catch (err) {
            const error = err as ApiError;
            const msg = error.response.data.message;
            showToast('Could not disconnect', msg!, 'error');
         } finally {
            setIsDisconnecting(false);
         }
      }
   };

   function onClick() {
      is_active ? void onDisconnect() : void onConnect();
   }
   /*useEffect(() => {
      const searchParams = new URLSearchParams(window.location.search);
      const fetchGA = searchParams.get('fetchGA');
     

      if (fetchGA) {
       fetchAccountProperties({ clientId: authUser?.client_id });
      }
   }, []);*/
   useEffect(() => {
      const searchParams = new URLSearchParams(window.location.search);
      const ga = searchParams.get('ga');
      const accessToken = searchParams.get('token');
      const refreshToken = searchParams.get('retoken');

      if (ga && accessToken && refreshToken && authUser?.client_id) {
         connectToSentiment({
            client_id: authUser.client_id,
            isConnect: true,
            access_token: accessToken,
            refresh_token: refreshToken,
         });
      }
   }, []);

  return (
  <>
    {isFetchingGA && <LoaderModal />}
    
    <Card
      error={errorMessage}
      isConnected={is_active}
      isDisconnecting={isDisconnecting}
      isConnecting={isConnectingToSentiment}
      isFetching={isLoading}
      onButtonClick={onClick}
      heading='Google Analytics'
      src={image}
    />
  </>
);

};

export default GoogleAnalytics;
