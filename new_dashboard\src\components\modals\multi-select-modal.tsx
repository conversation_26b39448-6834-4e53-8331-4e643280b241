import React, { useState } from 'react';
import { 
   Button, 
   VStack, 
   Text, 
   useColorModeValue,
   Flex,
   Box,
   Checkbox,
   CheckboxGroup,
   Stack
} from '@chakra-ui/react';
import ModalWrapper from './modal-wrapper';
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface MultiSelectModalProps {
   title?: string;
   options: Record<string, string>;
   onSelect: (selectedValues: string[] | null) => void;
   confirmButtonText?: string;
   showCancelButton?: boolean;
   minSelections?: number;
}

const MultiSelectModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);
   
   const {
      title = 'Select Options',
      options = {},
      onSelect,
      confirmButtonText = 'Save',
      showCancelButton = true,
      minSelections = 1,
   } = (payload?.modalProps || {}) as MultiSelectModalProps;

   const [selectedValues, setSelectedValues] = useState<string[]>([]);
   const [error, setError] = useState<string>('');

  /* const boxBg = useColorModeValue('gray.50', 'gray.700');
   const borderColor = useColorModeValue('gray.200', 'gray.600');*/
   const errorColor = useColorModeValue('red.500', 'red.300');

   const handleSelectionChange = (values: string[]) => {
      setSelectedValues(values);
      if (error) setError('');
   };

   const handleConfirm = () => {
      if (selectedValues.length < minSelections) {
         setError(`Please select at least ${minSelections} option${minSelections > 1 ? 's' : ''}.`);
         return;
      }
      
      onSelect(selectedValues);
      dispatch(closeModal());
   };

   const handleCancel = () => {
      onSelect(null);
      dispatch(closeModal());
   };

   const footer = (
      <Flex gap={3} justifyContent="flex-end" width="100%">
         {showCancelButton && (
            <Button
               variant="outline"
               onClick={handleCancel}
               colorScheme="gray"
            >
               Cancel
            </Button>
         )}
         <Button
            colorScheme="blue"
            onClick={handleConfirm}
            isDisabled={selectedValues.length < minSelections}
         >
            {confirmButtonText}
         </Button>
      </Flex>
   );

   return (
      <ModalWrapper
         heading={title}
         footer={footer}
         size="lg"
         closeOnEsc={true}
         closeOnOverlayClick={false}
         closeFunction={handleCancel}
      >
         <VStack spacing={4} align="stretch">
            <Box
                p={4}
   bg="white"
   border="1px solid"
   borderColor="gray.200"
   borderRadius="lg"
   _hover={{ borderColor: 'blue.400', boxShadow: 'sm' }}
   transition="all 0.2s"
            >
                 
               <CheckboxGroup
                  value={selectedValues}
                  onChange={handleSelectionChange}
                  colorScheme="blue"
               >
                  <Stack spacing={3}>
                     {Object.entries(options).map(([value, label]) => (
                        <Checkbox 
                           key={value} 
                           value={value}
                           size="md"
                        >
                           <Text fontSize="md">{label}</Text>
                        </Checkbox>
                     ))}
                  </Stack>
               </CheckboxGroup>
               
            </Box>
            
            {selectedValues.length > 0 && (
               <Text fontSize="sm" color="gray.600">
                  {selectedValues.length} option{selectedValues.length > 1 ? 's' : ''} selected
               </Text>
            )}
            
            {error && (
               <Text color={errorColor} fontSize="sm">
                  {error}
               </Text>
            )}
         </VStack>
      </ModalWrapper>
   );
};

export default MultiSelectModal;
