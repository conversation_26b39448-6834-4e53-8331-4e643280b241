import React, { useState } from 'react';
import { 
   Button, 
   VStack, 
   Text, 
   useColorModeValue,
   Flex,
   Box,
   Checkbox,
   CheckboxGroup,
   Stack
} from '@chakra-ui/react';
import ModalWrapper from './modal-wrapper';
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface MultiSelectModalProps {
   title?: string;
   options: Record<string, string>;
   onSelect: (selectedValues: string[] | null) => void;
   confirmButtonText?: string;
   showCancelButton?: boolean;
   minSelections?: number;
}

const MultiSelectModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);
   
   const {
      title = 'Select Options',
      options = {},
      onSelect,
      confirmButtonText = 'Save',
      //showCancelButton = true,
      minSelections = 1,
   } = (payload?.modalProps || {}) as MultiSelectModalProps;

   const [selectedValues, setSelectedValues] = useState<string[]>([]);
   const [error, setError] = useState<string>('');

  /* const boxBg = useColorModeValue('gray.50', 'gray.700');
   const borderColor = useColorModeValue('gray.200', 'gray.600');*/
   const errorColor = useColorModeValue('red.500', 'red.300');

   const handleSelectionChange = (values: string[]) => {
      setSelectedValues(values);
      if (error) setError('');
   };

   const handleConfirm = () => {
      if (selectedValues.length < minSelections) {
         setError(`Please select at least ${minSelections} option${minSelections > 1 ? 's' : ''}.`);
         return;
      }
      
      onSelect(selectedValues);
      dispatch(closeModal());
   };

   const handleCancel = () => {
      onSelect(null);
      dispatch(closeModal());
   };

   const footer = (
      <Flex gap={3} justifyContent="flex-end" width="100%">
         {/*{showCancelButton && (
            <Button
               variant="outline"
               onClick={handleCancel}
               colorScheme="gray"
            >
               Cancel
            </Button>
         )}*/}
         <Button
            colorScheme="blue"
            onClick={handleConfirm}
            isDisabled={selectedValues.length < minSelections}
         >
            {confirmButtonText}
         </Button>
      </Flex>
   );

   return (
      <ModalWrapper
         heading={title}
         footer={footer}
         size="lg"
         closeOnEsc={true}
         closeOnOverlayClick={false}
         closeFunction={handleCancel}
      >
         <VStack spacing={4} align="stretch">
            {/* Table-like container */}
            <Box
               border="1px solid"
               borderColor="gray.200"
               borderRadius="sm"
               overflow="hidden"
               bg="white"
            >
               {/* Table header */}
              

               {/* Options list */}
               <CheckboxGroup
                  value={selectedValues}
                  onChange={handleSelectionChange}
                  colorScheme="blue"
               >
                  <Stack spacing={0}>
                     {Object.entries(options).map(([value, label], index) => {
                        const isSelected = selectedValues.includes(value);
                        return (
                           <Box
                              key={value}
                              p={3}
                              borderBottom={index < Object.entries(options).length - 1 ? "1px solid" : "none"}
                              borderColor="gray.100"
                             bg={isSelected ? "#E8F0FE" : "white"}
                              _hover={{
                                 bg: isSelected ? "blue.80" : "gray.50"
                              }}
                              transition="all 0.2s"
                              cursor="pointer"
                              onClick={() => {
                                 const newValues = isSelected
                                    ? selectedValues.filter(v => v !== value)
                                    : [...selectedValues, value];
                                 handleSelectionChange(newValues);
                              }}
                           >
                              <Checkbox
                                 value={value}
                                 size="md"
                                 colorScheme="blue"
                                 isChecked={isSelected}
                                 pointerEvents="none"
                              >
                                 <Text
                                    fontSize="md"
                                    color={isSelected ? "blue.700" : "gray.700"}
                                    ml={2}
                                    fontWeight={isSelected ? "medium" : "normal"}
                                 >
                                    {label}
                                 </Text>
                              </Checkbox>
                           </Box>
                        );
                     })}
                  </Stack>
               </CheckboxGroup>
            </Box>

            {error && (
               <Text color={errorColor} fontSize="sm" mt={2}>
                  {error}
               </Text>
            )}
         </VStack>
      </ModalWrapper>
   );
};

export default MultiSelectModal;
