import React, { useState } from 'react';
import { 
   Button, 
   VStack, 
   Text, 
   useColorModeValue,
   Flex,
   Box,
   Checkbox,
   CheckboxGroup,
   Stack
} from '@chakra-ui/react';
import ModalWrapper from './modal-wrapper';
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface MultiSelectModalProps {
   title?: string;
   options: Record<string, string>;
   onSelect: (selectedValues: string[] | null) => void;
   confirmButtonText?: string;
   showCancelButton?: boolean;
   minSelections?: number;
}

const MultiSelectModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);
   
   const {
      title = 'Select Options',
      options = {},
      onSelect,
      confirmButtonText = 'Save',
      showCancelButton = true,
      minSelections = 1,
   } = (payload?.modalProps || {}) as MultiSelectModalProps;

   const [selectedValues, setSelectedValues] = useState<string[]>([]);
   const [error, setError] = useState<string>('');

  /* const boxBg = useColorModeValue('gray.50', 'gray.700');
   const borderColor = useColorModeValue('gray.200', 'gray.600');*/
   const errorColor = useColorModeValue('red.500', 'red.300');

   const handleSelectionChange = (values: string[]) => {
      setSelectedValues(values);
      if (error) setError('');
   };

   const handleConfirm = () => {
      if (selectedValues.length < minSelections) {
         setError(`Please select at least ${minSelections} option${minSelections > 1 ? 's' : ''}.`);
         return;
      }
      
      onSelect(selectedValues);
      dispatch(closeModal());
   };

   const handleCancel = () => {
      onSelect(null);
      dispatch(closeModal());
   };

   const footer = (
      <Flex gap={3} justifyContent="flex-end" width="100%">
         {showCancelButton && (
            <Button
               variant="outline"
               onClick={handleCancel}
               colorScheme="gray"
            >
               Cancel
            </Button>
         )}
         <Button
            colorScheme="blue"
            onClick={handleConfirm}
            isDisabled={selectedValues.length < minSelections}
         >
            {confirmButtonText}
         </Button>
      </Flex>
   );

   return (
      <ModalWrapper
         heading={title}
         footer={footer}
         size="lg"
         closeOnEsc={true}
         closeOnOverlayClick={false}
         closeFunction={handleCancel}
      >
         <VStack spacing={4} align="stretch">
            {/* Header with selection count */}
            <Flex
               justify="space-between"
               align="center"
               p={3}
               bg="blue.50"
               borderRadius="md"
               border="1px solid"
               borderColor="blue.200"
            >
               <Flex align="center" gap={2}>
                  <Box w={3} h={3} bg="blue.500" borderRadius="sm" />
                  <Text fontSize="sm" color="blue.700" fontWeight="medium">
                     {selectedValues.length} of {Object.keys(options).length} items selected
                  </Text>
               </Flex>
            </Flex>

            {/* Table-like container */}
            <Box
               border="1px solid"
               borderColor="gray.200"
               borderRadius="lg"
               overflow="hidden"
               bg="white"
            >
               {/* Table header */}
               <Box
                  p={4}
                  bg="gray.50"
                  borderBottom="1px solid"
                  borderColor="gray.200"
               >
                  <Text fontSize="sm" fontWeight="semibold" color="gray.700">
                     Document Name
                  </Text>
               </Box>

               {/* Options list */}
               <CheckboxGroup
                  value={selectedValues}
                  onChange={handleSelectionChange}
                  colorScheme="blue"
               >
                  <Stack spacing={0}>
                     {Object.entries(options).map(([value, label], index) => (
                        <Box
                           key={value}
                           p={4}
                           borderBottom={index < Object.entries(options).length - 1 ? "1px solid" : "none"}
                           borderColor="gray.100"
                           _hover={{
                              bg: "blue.25",
                              borderColor: "blue.200"
                           }}
                           transition="all 0.2s"
                           cursor="pointer"
                           onClick={() => {
                              const newValues = selectedValues.includes(value)
                                 ? selectedValues.filter(v => v !== value)
                                 : [...selectedValues, value];
                              handleSelectionChange(newValues);
                           }}
                        >
                           <Checkbox
                              value={value}
                              size="md"
                              colorScheme="blue"
                              isChecked={selectedValues.includes(value)}
                              pointerEvents="none"
                           >
                              <Text fontSize="md" color="gray.700" ml={2}>
                                 {label}
                              </Text>
                           </Checkbox>
                        </Box>
                     ))}
                  </Stack>
               </CheckboxGroup>
            </Box>

            {error && (
               <Text color={errorColor} fontSize="sm" mt={2}>
                  {error}
               </Text>
            )}
         </VStack>
      </ModalWrapper>
   );
};

export default MultiSelectModal;
